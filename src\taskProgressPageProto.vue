<template>
    <div class="env-container">
        <!-- 1. 关卡路径任务进度看板 -->
        <div class="progress-board">
            <svg class="progress-path" width="100%" height="140" viewBox="0 0 375 140">
                <!-- 蜿蜒路径（示意线，实际开发可替换为曲线） -->
                <polyline :points="pathPoints" fill="none" stroke="#ccc" stroke-width="12" :stroke-opacity="0.6" />
                <!-- 路径上的任务节点 -->
                <g v-for="(node, i) in nodes" :key="i">
                    <circle :cx="node.x" :cy="node.y" :r="20" :fill="nodeColor(node.status)"
                        :stroke="isCurrent(i) ? '#222' : '#666'" :stroke-width="isCurrent(i) ? 4 : 2"
                        @click="onNodeClick(i)" style="cursor: pointer" />
                    <text :x="node.x" :y="node.y + 5" text-anchor="middle" font-size="14"
                        :fill="isCurrent(i) ? '#000' : '#888'" font-weight="bold">{{ i + 1 }}</text>
                </g>
            </svg>
        </div>

        <div class="task-details-board">
            <!-- 2. 当前节点任务明细看板 -->
            <div class="task-details">
                <div class="task-title dark-block">任务：{{ currentNode.title }}</div>
                <div class="task-content gray-block">
                    <div>内容：{{ currentNode.content }}</div>
                    <div>完成标准：{{ currentNode.standard }}</div>
                </div>
                <div class="task-time light-block">
                    已用时：{{ currentNode.usedTime }} 分钟
                </div>
            </div>

            <!-- 3. 任务操作区 -->
            <div class="task-actions">
                <button class="btn dark-block" @click="editTask">编辑节点任务</button>
                <button class="btn gray-block" @click="completeTask"
                    :disabled="currentNode.status === 'completed'">完成任务</button>
                <button class="btn light-block" @click="recordOrClockIn">
                    {{ currentNode.type === "timer" ? "记时" : "打卡" }}
                </button>
            </div>
        </div>

        <!-- 4. 弹窗：心得/拍照记录 -->
        <div v-if="popupVisible" class="popup-mask" @click.self="popupVisible = false">
            <div class="popup-content">
                <div class="popup-title">心得/拍照记录</div>
                <textarea class="popup-input" v-model="popupNote" placeholder="记录心得..."></textarea>
                <div class="popup-actions">
                    <button class="btn dark-block" @click="saveNote">保存</button>
                    <button class="btn gray-block" @click="popupVisible = false">取消</button>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'

// 路径节点数据
const nodes = ref([
    // 这里只是示例，实际开发可从接口获取
    { x: 40, y: 100, status: 'completed', title: '任务一', content: '内容A', standard: '标准A', usedTime: 20, type: 'timer', note: '' },
    { x: 120, y: 60, status: 'completed', title: '任务二', content: '内容B', standard: '标准B', usedTime: 30, type: 'clock', note: '' },
    { x: 200, y: 80, status: 'inprogress', title: '任务三', content: '内容C', standard: '标准C', usedTime: 10, type: 'timer', note: '' },
    { x: 270, y: 120, status: 'pending', title: '任务四', content: '内容D', standard: '标准D', usedTime: 0, type: 'clock', note: '' },
    { x: 340, y: 100, status: 'pending', title: '任务五', content: '内容E', standard: '标准E', usedTime: 0, type: 'timer', note: '' },
])

// 当前节点索引（正在进行的第一个节点）
const currentIndex = computed(() => nodes.value.findIndex(n => n.status === 'inprogress'))
const currentNode = computed(() => nodes.value[currentIndex.value] ?? nodes.value[0])

// 路径点
const pathPoints = computed(() =>
    nodes.value.map(n => `${n.x},${n.y}`).join(' ')
)

// 节点颜色
function nodeColor(status: string) {
    if (status === 'completed') return '#A2D5A1' // 绿色
    if (status === 'inprogress') return '#FFD966' // 黄色
    return '#E0E0E0' // 灰色
}
function isCurrent(i: number) {
    return i === currentIndex.value
}

// 记录弹窗
const popupVisible = ref(false)
const popupNote = ref('')
let popupNodeIndex = -1

function onNodeClick(i: number) {
    // 只允许点击进行中或已完成
    const node = nodes.value[i]
    if (node.status === 'inprogress' || node.status === 'completed') {
        popupNote.value = node.note || ''
        popupNodeIndex = i
        popupVisible.value = true
    }
}

function saveNote() {
    if (popupNodeIndex >= 0) {
        nodes.value[popupNodeIndex].note = popupNote.value
        popupVisible.value = false
    }
}

// 操作区
function editTask() {
    // 跳转或弹窗编辑所有节点任务（此处只做占位）
    alert('进入节点任务编辑界面')
}

function completeTask() {
    if (currentIndex.value < 0) return
    // 当前节点完成
    nodes.value[currentIndex.value].status = 'completed'
    // 下一个未执行的节点标黄
    const next = nodes.value.findIndex((n, i) => i > currentIndex.value && n.status === 'pending')
    if (next >= 0) nodes.value[next].status = 'inprogress'
}

function recordOrClockIn() {
    // 记时或打卡占位
    if (currentNode.value.type === 'timer') {
        alert('开始/停止记时')
    } else {
        alert('打卡成功')
    }
}
</script>

<style scoped>
.env-container {
    background: #fff;
    min-height: 100vh;
    padding: 0;
    box-sizing: border-box;
    font-family: system-ui, sans-serif;
}

.progress-board {
    background: #f6f6f6;
    padding: 16px 0 0 0;
    border-bottom: 1.5px solid #ddd;
}

.progress-path {
    display: block;
    margin: 0 auto;
    width: 100%;
    height: 140px;
}

.task-details-board {
    padding: 20px 16px 16px 16px;
}

.task-details {
    margin-bottom: 16px;
}

.task-title {
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 8px;
    padding: 10px 12px;
    border-radius: 10px;
}

.task-content {
    padding: 16px 12px;
    border-radius: 8px;
    margin-bottom: 8px;
    font-size: 15px;
    line-height: 1.6;
}

.task-time {
    font-size: 14px;
    padding: 8px 12px;
    border-radius: 8px;
    margin-bottom: 6px;
}

.task-actions {
    display: flex;
    gap: 10px;
    margin-top: 8px;
}

.btn {
    flex: 1 1 1px;
    padding: 12px 0;
    font-size: 16px;
    border: none;
    border-radius: 8px;
    color: #222;
    font-weight: bold;
    cursor: pointer;
    transition: background 0.15s;
}

.btn:disabled {
    opacity: 0.4;
    cursor: not-allowed;
}

.dark-block {
    background: #222;
    color: #fff;
}

.gray-block {
    background: #bdbdbd;
    color: #222;
}

.light-block {
    background: #ededed;
    color: #222;
}

/* 弹窗 */
.popup-mask {
    position: fixed;
    left: 0;
    top: 0;
    width: 100vw;
    height: 100vh;
    background: rgba(2, 2, 2, 0.35);
    z-index: 30;
    display: flex;
    align-items: center;
    justify-content: center;
}

.popup-content {
    background: #fff;
    border-radius: 16px;
    padding: 20px;
    min-width: 80vw;
    max-width: 90vw;
    box-sizing: border-box;
    box-shadow: 0 7px 28px rgba(0, 0, 0, 0.18);
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.popup-title {
    font-size: 17px;
    font-weight: bold;
    margin-bottom: 8px;
}

.popup-input {
    width: 100%;
    min-height: 80px;
    border-radius: 8px;
    border: 1.5px solid #ddd;
    padding: 8px;
    background: #f6f6f6;
    font-size: 15px;
}

.popup-actions {
    display: flex;
    gap: 10px;
    justify-content: flex-end;
}
</style>